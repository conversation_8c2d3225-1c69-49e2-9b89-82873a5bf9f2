## PT-1: Insecure SSL/TLS Configuration

### Description

We identified that the servers in scope did not support TLS
1.3.

| IP Address | TCP Port | SSLv2 | SSLv3 | TLS 1.0 | TLS 1.1 | TLS 1.2 | TLS 1.3 |
|------------|----------|-------|-------|---------|---------|---------|---------|
| unknown | 443 | Disabled | Disabled | Disabled | Disabled | Disabled | Not supported |
| ********** | 443 | Disabled | Disabled | Disabled | Disabled | Enabled | Not supported |
| ************* | 443 | Disabled | Disabled | Disabled | Disabled | Enabled | Not supported |

  : : Identified SSL/TLS protocols

Furthermore, we identified the following insecure SSL/TLS settings on
the server[s] in scope:

| IP Address | TCP Port | RC4 block ciphers | 3DES block ciphers | Export cipher suites | Obsolete CBC ciphers | PFS |
|------------|----------|-------------------|--------------------|--------------------|---------------------|-----|
| unknown | 443 | Disabled | Disabled | Disabled | Disabled | Disabled |
| ********** | 443 | Disabled | Disabled | Disabled | Disabled | Enabled |
| ************* | 443 | Disabled | Disabled | Disabled | Disabled | Enabled |

  : : Identified SSL/TLS cipher suite properties

We also identified that one or more services were using a
1024-bit Diffie-Hellman group, which could make the server vulnerable to
the Logjam attack.

The following screenshot shows the potential attacks based on the
results of the "testssl.sh" tool:

<figure>
<img src="media/image1.jpg" style="width:6.21667in;height:4.13167in"
alt="Testssl.sh output" />
<figcaption><p>: Potential SSL/TLS protocol
vulnerabilities</p></figcaption>
</figure>

### Impact

Successful exploitation could allow an attacker to decrypt and read or
modify intercepted traffic that may contain sensitive information, such
as a user's login password or other personal information. However, the
vulnerability only affects users for whom the attacker can intercept
traffic. Confidentiality and integrity impacts are limited to the
context of the intercepted user.

### Likelihood

The likelihood of exploiting such vulnerabilities depends on several
factors, including the deployment of the systems involved, the security
controls in place and the behavior of the users accessing the systems.

While the attacker does not need to be authenticated to the service,
they would need to be on designated networks in a Man-in-the-Middle
position to abuse these issues.

### Recommendation

Improve the TLS/SSL configuration of the affected systems by considering
the following:

- For SSL/TLS protocols:
- Enable support for the TLS version 1.2 protocol.
- Consider enabling support for the TLS version 1.3 protocol.

- For SSL/TLS cipher suites:
- Investigate whether TLS compression is required by application users. If possible, disable TLS compression because it exposes users to the CRIME attack.
- Investigate whether HTTP compression is required by application users. If possible, disable HTTP compression because it exposes users to the BREACH attack.
- Change the TLS configuration to use a Diffie-Hellman group that is at least 2048 bit and unique, as the one currently used exposes the service to the Logjam attack.

*Note: Disabling old and insecure SSL versions (such as SSLv3, TLS 1.0
or TLS 1.1) will improve security, however, outdated clients may not
support secure versions such as TLS 1.2 or TLS 1.3. Disabling cipher
suites using CBC might also affect out-of-date clients. This could mean
that clients (human users via web browsers or services using APIs) of
the application will not be able to connect to the application.
Therefore, we recommend researching whether the most secure TLS versions
and cipher suites support all use cases.*

### Reference

National Institute of Standards and Technology. "Guidelines for the
Selection, Configuration, and Use of Transport Layer Security (TLS)
Implementations", NIST SP 800-52 Revision 2:
https://csrc.nist.gov/publications/detail/sp/800-52/rev-2/final,
published August 2019.

The following CVE[s] [are] connected to the finding
mentioned above:

| CVE Identifier | Vulnerability Name | Publish Date | CVSSv2 Score | Access | Complexity |
|----------------|-------------------|--------------|--------------|--------|------------|| CVE-2016-2183 | SWEET32, DES | 2016-08-31 | 5.0 | Remote | Low || CVE-2016-6329 | SWEET32, Blowfish | 2017-01-31 | 4.3 | Remote | Medium || CVE-2016-0800 | DROWN | 2016-03-01 | 4.3 | Remote | Medium || CVE-2015-4000 | LOGJAM | 2015-05-20 | 4.3 | Remote | Medium || CVE-2015-2808 | RC4 | 2015-03-31 | 4.3 | Remote | Medium || CVE-2015-0204 | FREAK | 2015-01-08 | 4.3 | Remote | Medium || CVE-2014-3566 | POODLE | 2014-10-04 | 4.3 | Remote | Medium || CVE-2011-3389 | BEAST | 2011-09-10 | 4.3 | Remote | Medium || CVE-2013-3587 | BREACH | 2013-08-01 | 4.3 | Remote | High || CVE-2012-4929 | CRIME | 2012-09-15 | 2.6 | Remote | High || CVE-2013-0169 | LUCKY 13 | 2013-02-08 | 2.6 | Remote | High |
  : : CVE ID and CVSS score

### Status and Retest Notes

Open